<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vue 固定表格测试</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* 固定表格样式 */
        .sticky-table-container {
            position: relative;
            border: 1px solid #e5e7eb;
            border-radius: 0.5rem;
        }

        .sticky-table {
            border-collapse: separate;
            border-spacing: 0;
        }

        /* 固定表头 */
        .sticky-header {
            position: sticky;
            top: 0;
            z-index: 20;
        }

        .sticky-header th {
            border-bottom: 1px solid #e5e7eb;
        }

        /* 固定列样式 */
        .sticky-column {
            position: sticky;
            z-index: 10;
        }

        .sticky-column-1 {
            left: 0;
            z-index: 15;
            width: 84px;
        }

        .sticky-column-2 {
            left: 84px;
            z-index: 15;
            min-width: 192px;
        }

        /* 表头固定列需要更高的z-index */
        .sticky-header .sticky-column {
            z-index: 25;
        }

        /* 表格滚动条样式 */
        .sticky-table-container::-webkit-scrollbar {
            width: 6px;
            height: 6px;
        }

        .sticky-table-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .sticky-table-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .sticky-table-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        .sticky-table-container::-webkit-scrollbar-corner {
            background: #f1f1f1;
        }

        /* 表格边框处理 */
        .sticky-table td,
        .sticky-table th {
            border-bottom: 1px solid #e5e7eb;
        }

        .sticky-table tbody tr:last-child td {
            border-bottom: none;
        }

        /* 阴影效果，增强固定列的视觉效果 */
        .sticky-column-1::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
            pointer-events: none;
        }

        .sticky-column-2::after {
            content: '';
            position: absolute;
            top: 0;
            right: -1px;
            bottom: 0;
            width: 1px;
            background: linear-gradient(to right, rgba(0, 0, 0, 0.1), transparent);
            pointer-events: none;
        }
    </style>
</head>
<body class="bg-gray-100 p-8">
    <div id="app">
        <div class="max-w-full mx-auto">
            <h1 class="text-2xl font-bold mb-6">Vue 固定表格测试</h1>
            
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <!-- 表格头部操作栏 -->
                <div class="px-6 py-4 border-b border-gray-200 flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <label class="flex items-center">
                            <input
                                type="checkbox"
                                :checked="isAllSelected"
                                @change="toggleSelectAll"
                                class="mr-2"
                            />
                            <span class="text-sm font-medium">
                                全选 ({{ selectedCount }}/{{ tableData.length }})
                            </span>
                        </label>
                    </div>
                </div>

                <!-- 表格主体 -->
                <div class="overflow-auto max-h-[600px] sticky-table-container">
                    <table class="w-full sticky-table">
                        <thead class="bg-gray-50 sticky-header">
                            <tr>
                                <th class="sticky-column sticky-column-1 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12 bg-gray-50 border-r border-gray-200">
                                    选择
                                </th>
                                <th class="sticky-column sticky-column-2 px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-48 bg-gray-50 border-r border-gray-200">
                                    Key
                                </th>
                                <th v-for="language in languages" :key="language" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-48">
                                    {{ language }}
                                </th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-32">
                                    操作
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <tr
                                v-for="(item, index) in tableData"
                                :key="item.key"
                                :class="{ 'bg-blue-50': item.selected }"
                            >
                                <!-- 选择列 -->
                                <td
                                    class="sticky-column sticky-column-1 px-6 py-4 whitespace-nowrap border-r border-gray-200"
                                    :class="item.selected ? 'bg-blue-50' : 'bg-white'"
                                >
                                    <input
                                        type="checkbox"
                                        v-model="item.selected"
                                        class="rounded"
                                    />
                                </td>

                                <!-- Key列 -->
                                <td
                                    class="sticky-column sticky-column-2 px-6 py-4 whitespace-nowrap border-r border-gray-200"
                                    :class="item.selected ? 'bg-blue-50' : 'bg-white'"
                                >
                                    <div class="text-sm font-medium text-gray-900">
                                        {{ item.key }}
                                    </div>
                                </td>

                                <!-- 各语言列 -->
                                <td
                                    v-for="language in languages"
                                    :key="language"
                                    class="px-6 py-4"
                                >
                                    <div class="text-sm text-gray-900">
                                        <textarea
                                            v-model="item.translations[language]"
                                            class="w-full min-h-[2.5rem] px-2 py-1 border border-gray-300 rounded resize-none focus:outline-none focus:ring-1 focus:ring-blue-500"
                                            :placeholder="`${language}内容`"
                                            rows="1"
                                        ></textarea>
                                    </div>
                                </td>

                                <!-- 操作列 -->
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    <button class="text-blue-600 hover:text-blue-900 text-xs px-2 py-1 border border-blue-600 rounded hover:bg-blue-50">
                                        翻译
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div class="mt-6 text-sm text-gray-600">
                <p><strong>测试说明：</strong></p>
                <ul class="list-disc list-inside mt-2 space-y-1">
                    <li>表格支持垂直和水平滚动</li>
                    <li>第一行（表头）在垂直滚动时保持固定</li>
                    <li>前两列（选择和Key列）在水平滚动时保持固定</li>
                    <li>固定列有阴影效果以增强视觉区分</li>
                    <li>选中行的背景色在固定列中也会正确显示</li>
                    <li>全选功能正常工作</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;

        createApp({
            setup() {
                const languages = ref(['中文', '英文', '日文', '韩文', '法文', '德文', '西班牙文', '意大利文']);
                
                // 生成测试数据
                const tableData = ref([]);
                for (let i = 1; i <= 50; i++) {
                    const translations = {};
                    languages.value.forEach(lang => {
                        translations[lang] = `${lang}测试内容 ${i}`;
                    });
                    
                    tableData.value.push({
                        key: `key_${i.toString().padStart(3, '0')}`,
                        selected: i % 5 === 0, // 每5个选中一个
                        translations: translations
                    });
                }

                const selectedCount = computed(() => {
                    return tableData.value.filter(item => item.selected).length;
                });

                const isAllSelected = computed(() => {
                    return tableData.value.length > 0 && selectedCount.value === tableData.value.length;
                });

                const toggleSelectAll = () => {
                    const newState = !isAllSelected.value;
                    tableData.value.forEach(item => {
                        item.selected = newState;
                    });
                };

                return {
                    languages,
                    tableData,
                    selectedCount,
                    isAllSelected,
                    toggleSelectAll
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
