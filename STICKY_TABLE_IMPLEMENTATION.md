# 固定表格实现说明

## 功能概述

为翻译管理页面的表格实现了滚动时固定第一行（表头）和前两列（选择列和Key列）的功能。

## 实现特性

### 1. 固定表头
- 在垂直滚动时，表头始终保持在顶部可见
- 使用 `position: sticky` 和 `top: 0` 实现
- 设置了适当的 `z-index` 确保层级正确

### 2. 固定前两列
- **第一列（选择列）**: 包含复选框，用于选择行
- **第二列（Key列）**: 显示翻译项目的键值
- 在水平滚动时，这两列始终保持在左侧可见
- 使用 `position: sticky` 和 `left` 属性实现

### 3. 视觉增强
- 固定列添加了右侧阴影效果，增强视觉区分
- 固定列有边框分隔线
- 选中行的背景色在固定列中也会正确显示

### 4. 响应式设计
- 表格容器设置了最大高度（600px）
- 支持垂直和水平滚动
- 自定义滚动条样式

## 技术实现

### CSS 关键样式

```css
/* 表格容器 */
.sticky-table-container {
  position: relative;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: auto;
  max-height: 600px;
}

/* 固定表头 */
.sticky-header {
  position: sticky;
  top: 0;
  z-index: 20;
}

/* 固定列 */
.sticky-column {
  position: sticky;
  z-index: 10;
}

.sticky-column-1 {
  left: 0;
  z-index: 15;
  width: 84px;
}

.sticky-column-2 {
  left: 84px;
  z-index: 15;
  min-width: 192px;
}

/* 表头固定列需要更高的z-index */
.sticky-header .sticky-column {
  z-index: 25;
}
```

### Vue 模板结构

```vue
<div class="overflow-auto max-h-[600px] sticky-table-container">
  <table class="w-full sticky-table">
    <thead class="bg-gray-50 sticky-header">
      <tr>
        <th class="sticky-column sticky-column-1 ...">选择</th>
        <th class="sticky-column sticky-column-2 ...">Key</th>
        <th v-for="language in displayLanguages" ...>{{ language }}</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      <tr v-for="item in paginatedData" ...>
        <td class="sticky-column sticky-column-1 ..." :class="item.selected ? 'bg-blue-50' : 'bg-white'">
          <input type="checkbox" v-model="item.selected" />
        </td>
        <td class="sticky-column sticky-column-2 ..." :class="item.selected ? 'bg-blue-50' : 'bg-white'">
          {{ item.key }}
        </td>
        <td v-for="language in displayLanguages" ...>
          <textarea v-model="item.translations[language]" ...></textarea>
        </td>
        <td>操作按钮</td>
      </tr>
    </tbody>
  </table>
</div>
```

## 关键技术点

### 1. Z-Index 层级管理
- 普通单元格: z-index: 10
- 固定列: z-index: 15
- 固定表头: z-index: 20
- 固定表头中的固定列: z-index: 25

### 2. 背景色处理
- 使用 Vue 的动态类绑定 `:class` 来处理选中行的背景色
- 确保固定列的背景色与行选中状态同步

### 3. 列宽设置
- 第一列（选择列）: 固定宽度 84px
- 第二列（Key列）: 最小宽度 192px
- 其他列: 最小宽度 192px，可以根据内容自适应

### 4. 滚动条样式
- 自定义 webkit 滚动条样式
- 设置了滚动条的宽度、颜色和圆角

## 浏览器兼容性

- 现代浏览器（Chrome, Firefox, Safari, Edge）完全支持
- 使用了 `position: sticky`，IE 不支持
- 使用了 CSS Grid 和 Flexbox，需要现代浏览器

## 测试文件

项目中包含了两个测试文件用于验证功能：

1. `test-sticky-table.html` - 纯 HTML/CSS 实现的测试页面
2. `test-vue-sticky-table.html` - Vue 3 实现的测试页面

这些测试文件可以在浏览器中直接打开，用于验证固定表格功能是否正常工作。

## 使用说明

1. 表格会在内容超出容器高度时显示垂直滚动条
2. 表格会在内容超出容器宽度时显示水平滚动条
3. 滚动时，第一行（表头）始终保持在顶部
4. 滚动时，前两列（选择和Key列）始终保持在左侧
5. 选中行时，固定列的背景色会与其他列保持一致

## 性能考虑

- 使用了 CSS 的 `position: sticky` 而不是 JavaScript 实现，性能更好
- 表格使用了分页，避免一次性渲染大量数据
- 固定列的阴影效果使用了 CSS 伪元素，避免额外的 DOM 元素
