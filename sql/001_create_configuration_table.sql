-- 创建configuration表
-- 用于存储系统配置信息

CREATE TABLE IF NOT EXISTS configuration (
    id SERIAL PRIMARY KEY,
    key VARCHAR(255) NOT NULL,
    value TEXT,
    create_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT uk_configuration_key UNIQUE (key)
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_configuration_key ON configuration(key);

-- 创建更新时间触发器
CREATE OR REPLACE FUNCTION update_configuration_updated_time()
RETURNS TRIGGER AS $$
BEGIN
    NEW.update_time = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_configuration_time
    BEFORE UPDATE ON configuration
    FOR EACH ROW
    EXECUTE FUNCTION update_configuration_updated_time();

-- 插入默认配置
INSERT INTO configuration (key, value) VALUES
    ('QWEN_API_KEY', NULL),
ON CONFLICT (key) DO NOTHING;

-- 添加表注释
COMMENT ON TABLE configuration IS '系统配置表';
COMMENT ON COLUMN configuration.id IS '主键ID';
COMMENT ON COLUMN configuration.key IS '配置键名';
COMMENT ON COLUMN configuration.value IS '配置值';
COMMENT ON COLUMN configuration.create_time IS '创建时间';
COMMENT ON COLUMN configuration.update_time IS '更新时间';
